#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的股票分析系统
验证关键功能是否正常工作
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from indicator_analysis import StockDataManager, StockEchartVisualizer, ConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_config_manager():
    """测试配置管理器"""
    print("=" * 50)
    print("测试配置管理器")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager()
        api_key = config_manager.get_api_key("test_default_key")
        if api_key:
            print(f"[OK] 配置管理器工作正常，API密钥: {api_key[:10]}...")
        else:
            print("[OK] 配置管理器工作正常，API密钥为空")
        return True
    except Exception as e:
        print(f"[FAIL] 配置管理器测试失败: {e}")
        return False

def test_data_manager():
    """测试数据管理器"""
    print("=" * 50)
    print("测试数据管理器")
    print("=" * 50)
    
    try:
        # 使用测试缓存目录
        cache_dir = Path(__file__).parent / "test_cache"
        data_manager = StockDataManager(cache_dir=cache_dir)
        
        # 测试获取基金列表
        funds = data_manager.get_available_funds()
        print(f"[OK] 数据管理器初始化成功，可用基金数量: {len(funds)}")

        # 测试缓存功能
        test_code = "513260.SH"
        is_valid = data_manager.is_cache_valid(test_code)
        print(f"[OK] 缓存检查功能正常，{test_code} 缓存状态: {'有效' if is_valid else '无效'}")

        return True
    except Exception as e:
        print(f"[FAIL] 数据管理器测试失败: {e}")
        return False

def test_visualizer():
    """测试可视化器"""
    print("=" * 50)
    print("测试可视化器")
    print("=" * 50)
    
    try:
        # 使用测试缓存目录
        cache_dir = Path(__file__).parent / "test_cache"
        config = {"theme": "white", "width": 800, "height": 600}
        visualizer = StockEchartVisualizer(config, cache_dir=cache_dir)
        
        print("[OK] 可视化器初始化成功")
        print(f"[OK] 主题: {visualizer.theme}")
        print(f"[OK] 尺寸: {visualizer.width}x{visualizer.height}")

        return True
    except Exception as e:
        print(f"[FAIL] 可视化器测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接管理"""
    print("=" * 50)
    print("测试数据库连接管理")
    print("=" * 50)
    
    try:
        cache_dir = Path(__file__).parent / "test_cache"
        data_manager = StockDataManager(cache_dir=cache_dir)
        
        # 测试数据库连接上下文管理器
        with data_manager.get_db_connection() as conn:
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"[OK] 数据库连接正常，表数量: {len(tables)}")

        return True
    except Exception as e:
        print(f"[FAIL] 数据库连接测试失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("=" * 50)
    print("测试错误处理")
    print("=" * 50)

    try:
        # 测试无效API密钥处理
        try:
            data_manager = StockDataManager(api_key="")
            print("[FAIL] 应该抛出API密钥错误")
            return False
        except ValueError as e:
            print(f"[OK] API密钥验证正常: {e}")

        # 测试无效缓存目录处理
        cache_dir = Path(__file__).parent / "test_cache"
        data_manager = StockDataManager(cache_dir=cache_dir)

        # 测试无效股票代码
        invalid_data = data_manager.get_fund_data("INVALID.CODE", force_refresh=True)
        print(f"[OK] 无效代码处理正常，返回数据长度: {len(invalid_data)}")

        return True
    except Exception as e:
        print(f"[FAIL] 错误处理测试失败: {e}")
        return False

def test_technical_indicators():
    """测试技术指标计算"""
    print("=" * 50)
    print("测试技术指标计算")
    print("=" * 50)

    try:
        from indicator_analysis import TechnicalIndicator
        import numpy as np

        # 创建测试数据
        test_data = np.array([10, 11, 12, 11, 10, 9, 10, 11, 12, 13, 12, 11, 10])
        test_high = test_data + 0.5
        test_low = test_data - 0.5

        # 测试RSI计算
        rsi = TechnicalIndicator.calculate_rsi(test_data)
        print(f"[OK] RSI计算正常，最后值: {rsi[-1]:.2f}")

        # 测试MACD计算
        dif, dea, macd = TechnicalIndicator.calculate_macd(test_data)
        print(f"[OK] MACD计算正常，DIF: {dif[-1]:.4f}, DEA: {dea[-1]:.4f}, MACD: {macd[-1]:.4f}")

        # 测试KDJ计算（使用修复后的参数）
        k, d, j = TechnicalIndicator.calculate_kdj(test_high, test_low, test_data, k_period=9, d_period=3, j_period=3)
        print(f"[OK] KDJ计算正常，K: {k[-1]:.2f}, D: {d[-1]:.2f}, J: {j[-1]:.2f}")

        return True
    except Exception as e:
        print(f"[FAIL] 技术指标测试失败: {e}")
        return False

def cleanup_test_files():
    """清理测试文件"""
    try:
        test_cache_dir = Path(__file__).parent / "test_cache"
        if test_cache_dir.exists():
            import shutil
            shutil.rmtree(test_cache_dir)
            print("✓ 测试文件清理完成")
    except Exception as e:
        print(f"⚠ 测试文件清理失败: {e}")

def main():
    """主测试函数"""
    print("开始测试优化后的股票分析系统...")
    print()
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("配置管理器", test_config_manager()))
    test_results.append(("数据管理器", test_data_manager()))
    test_results.append(("可视化器", test_visualizer()))
    test_results.append(("数据库连接", test_database_connection()))
    test_results.append(("错误处理", test_error_handling()))
    test_results.append(("技术指标", test_technical_indicators()))
    
    # 显示测试结果
    print()
    print("=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print()
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统优化成功。")
    else:
        print("⚠ 部分测试失败，请检查相关功能。")
    
    # 清理测试文件
    cleanup_test_files()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
